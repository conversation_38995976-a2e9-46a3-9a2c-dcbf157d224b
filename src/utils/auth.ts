// 认证相关工具函数

// 创建一个自定义事件来处理 401 登出
export const createLogoutEvent = () => {
  const event = new CustomEvent('auth:logout', {
    detail: { reason: '401_unauthorized' }
  });
  window.dispatchEvent(event);
};

// 处理 401 未授权错误
export const handle401Error = () => {
  // 触发登出事件
  createLogoutEvent();
  
  // 延迟跳转，给 store 时间清理状态
  setTimeout(() => {
    window.location.href = '/backend/login';
  }, 100);
};

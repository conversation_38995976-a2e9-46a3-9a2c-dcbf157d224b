import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import { Spin } from 'antd';

// 懒加载组件
const Login = lazy(() => import('@/views/Login'));
const NotFound = lazy(() => import('@/views/NotFound'));
const AdminLayout = lazy(() => import('@/components/Layout'));
const ProtectedRoute = lazy(() => import('@/components/ProtectedRoute'));
const ChannelUsers = lazy(() => import('@/views/ChannelUsers'));
const ChannelPayments = lazy(() => import('@/views/ChannelPayments'));

// 加载组件包装器
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense 
    fallback={
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    }
  >
    {children}
  </Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/backend/channel-users" replace />,
  },
  {
    path: '/backend/login',
    element: (
      <LazyWrapper>
        <Login />
      </LazyWrapper>
    ),
  },
  {
    path: '/backend',
    element: (
      <LazyWrapper>
        <ProtectedRoute>
          <AdminLayout />
        </ProtectedRoute>
      </LazyWrapper>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="channel-users" replace />,
      },
      {
        path: 'channel-users',
        element: (
          <LazyWrapper>
            <ChannelUsers />
          </LazyWrapper>
        ),
      },
      {
        path: 'channel-payment',
        element: (
          <LazyWrapper>
            <ChannelPayments />
          </LazyWrapper>
        ),
      },
    ],
  },
  // 404 页面
  {
    path: '*',
    element: (
      <LazyWrapper>
        <NotFound />
      </LazyWrapper>
    ),
  },
]);

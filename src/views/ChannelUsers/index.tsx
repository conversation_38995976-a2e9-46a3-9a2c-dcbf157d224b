import React, { useState, useEffect } from 'react';
import { Table, Card, Form, Button, DatePicker, Select, Tag, message } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { channelApi } from '@/api/channel';
import { useAdminStore } from '@/store/adminStore';
import type {
  ChannelUserResponse,
  ChannelUsersQueryParams,
  UserType
} from '@/api/types/channel';

const { RangePicker } = DatePicker;
const { Option } = Select;

const ChannelUsers: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ChannelUserResponse[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取管理员信息中的渠道列表
  const { adminInfo } = useAdminStore();



  // 获取用户类型标签
  const getUserTypeTag = (userType: UserType) => {
    switch (userType) {
      case 1:
        return <Tag color="default">游客</Tag>;
      case 2:
        return <Tag color="blue">普通用户</Tag>;
      case 3:
        return <Tag color="gold">会员</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  // 表格列定义
  const columns: ColumnsType<ChannelUserResponse> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      ellipsis: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      width: 100,
      render: (channel: string) => (
        <Tag color="blue">{channel}</Tag>
      ),
    },

    {
      title: '用户类型',
      dataIndex: 'user_type',
      key: 'user_type',
      width: 100,
      render: (userType: UserType) => getUserTypeTag(userType),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  // 获取数据
  const fetchData = async (params?: Partial<ChannelUsersQueryParams>) => {
    setLoading(true);
    try {
      const queryParams: ChannelUsersQueryParams = {
        page: pagination.current,
        page_size: pagination.pageSize,
        ...params,
      };

      const response = await channelApi.getChannelUsers(queryParams);
      
      setData(response.list);
      setPagination(prev => ({
        ...prev,
        total: response.total,
        current: response.page,
        pageSize: response.page_size,
      }));
    } catch (error) {
      message.error('获取渠道用户数据失败');
      console.error('获取渠道用户数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params: Partial<ChannelUsersQueryParams> = {
      page: 1, // 搜索时重置到第一页
    };

    if (values.channel) {
      params.channel = values.channel;
    }

    if (values.timeRange && values.timeRange.length === 2) {
      params.start_time = values.timeRange[0].toISOString();
      params.end_time = values.timeRange[1].toISOString();
    }

    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData(params);
  };

  // 重置搜索
  const handleReset = () => {
    const todayRange = getTodayRange();

    if (adminInfo?.is_super_admin) {
      // 超级管理员可以完全重置，但保持今天的时间范围
      form.resetFields();
      form.setFieldsValue({ timeRange: todayRange });
      setPagination(prev => ({ ...prev, current: 1 }));
      fetchData({
        page: 1,
        start_time: todayRange[0].toISOString(),
        end_time: todayRange[1].toISOString()
      });
    } else {
      // 普通管理员重置时保持默认渠道和今天的时间范围
      const defaultChannel = adminInfo?.channels?.[0];
      form.resetFields();
      if (defaultChannel) {
        form.setFieldsValue({
          channel: defaultChannel,
          timeRange: todayRange
        });
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchData({
          page: 1,
          channel: defaultChannel,
          start_time: todayRange[0].toISOString(),
          end_time: todayRange[1].toISOString()
        });
      } else {
        form.setFieldsValue({ timeRange: todayRange });
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchData({
          page: 1,
          start_time: todayRange[0].toISOString(),
          end_time: todayRange[1].toISOString()
        });
      }
    }
  };

  // 表格分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    const newPagination = { ...pagination, current: page, pageSize };
    setPagination(newPagination);
    
    const values = form.getFieldsValue();
    const params: Partial<ChannelUsersQueryParams> = {
      page,
      page_size: pageSize,
    };

    if (values.channel) {
      params.channel = values.channel;
    }

    if (values.timeRange && values.timeRange.length === 2) {
      params.start_time = values.timeRange[0].toISOString();
      params.end_time = values.timeRange[1].toISOString();
    }

    fetchData(params);
  };

  // 获取今天的时间范围
  const getTodayRange = () => {
    const today = dayjs();
    return [
      today.startOf('day'), // 今天 00:00:00
      today.endOf('day')    // 今天 23:59:59
    ];
  };

  // 初始化数据和默认渠道
  useEffect(() => {
    // 设置默认时间范围为今天
    const todayRange = getTodayRange();

    if (adminInfo?.is_super_admin) {
      // 超级管理员不设置默认渠道，但设置默认时间范围
      form.setFieldsValue({ timeRange: todayRange });
      fetchData({
        start_time: todayRange[0].toISOString(),
        end_time: todayRange[1].toISOString()
      });
    } else if (adminInfo?.channels && adminInfo.channels.length > 0) {
      // 普通管理员设置默认渠道为第一个，并设置默认时间范围
      const defaultChannel = adminInfo.channels[0];
      form.setFieldsValue({
        channel: defaultChannel,
        timeRange: todayRange
      });
      fetchData({
        channel: defaultChannel,
        start_time: todayRange[0].toISOString(),
        end_time: todayRange[1].toISOString()
      });
    } else {
      // 如果没有渠道信息，设置默认时间范围并查询
      form.setFieldsValue({ timeRange: todayRange });
      fetchData({
        start_time: todayRange[0].toISOString(),
        end_time: todayRange[1].toISOString()
      });
    }
  }, [adminInfo?.channels, adminInfo?.is_super_admin]);

  return (
    <div className="p-6">
      <Card className="mb-4">
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          className="mb-4"
        >
          <Form.Item name="channel" label="渠道号">
            <Select
              placeholder="请选择渠道号"
              style={{ width: 200 }}
              allowClear={adminInfo?.is_super_admin || false}
            >
              {adminInfo?.channels?.map((channel: string) => (
                <Option key={channel} value={channel}>
                  {channel}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="timeRange" label="时间范围">
            <RangePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              style={{ width: 300 }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
          </Form.Item>

          <Form.Item>
            <Button onClick={handleReset} icon={<ReloadOutlined />}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card title="渠道用户列表">
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  );
};

export default ChannelUsers;

import React, { useState, useEffect } from 'react';
import { Table, Card, Form, Button, DatePicker, Select, Tag, message, Statistic, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined, DollarOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { channelApi } from '@/api/channel';
import { useAdminStore } from '@/store/adminStore';
import type {
  ChannelPaymentResponse,
  ChannelPaymentsQueryParams,
  PaymentMethod
} from '@/api/types/channel';

const { RangePicker } = DatePicker;
const { Option } = Select;

const ChannelPayments: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ChannelPaymentResponse[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取管理员信息中的渠道列表
  const { adminInfo } = useAdminStore();



  // 获取支付方式标签
  const getPaymentMethodTag = (method: PaymentMethod) => {
    switch (method) {
      case 1: // WECHAT
        return <Tag color="green">微信支付</Tag>;
      case 2: // ALIPAY
        return <Tag color="blue">支付宝</Tag>;
      default:
        return <Tag color="default">未知方式</Tag>;
    }
  };

  // 表格列定义
  const columns: ColumnsType<ChannelPaymentResponse> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户ID',
      dataIndex: 'user_id',
      key: 'user_id',
      width: 100,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      width: 100,
      render: (channel: string) => (
        <Tag color="blue">{channel}</Tag>
      ),
    },
    {
      title: '订单号',
      dataIndex: 'order_no',
      key: 'order_no',
      width: 180,
      ellipsis: true,
    },
    {
      title: '付费金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number) => (
        <span className="text-red-500 font-medium">
          ¥{amount.toFixed(2)}
        </span>
      ),
    },
    {
      title: '支付方式',
      dataIndex: 'payment_method',
      key: 'payment_method',
      width: 100,
      render: (method: PaymentMethod) => getPaymentMethodTag(method),
    },

    {
      title: '订单时间',
      dataIndex: 'order_created_at',
      key: 'order_created_at',
      width: 180,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '用户注册时间',
      dataIndex: 'user_created_at',
      key: 'user_created_at',
      width: 180,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  // 获取数据
  const fetchData = async (params?: Partial<ChannelPaymentsQueryParams>) => {
    setLoading(true);
    try {
      const queryParams: ChannelPaymentsQueryParams = {
        page: pagination.current,
        page_size: pagination.pageSize,
        ...params,
      };

      const response = await channelApi.getChannelPayments(queryParams);
      
      setData(response.list);
      setTotalAmount(response.total_amount);
      setPagination(prev => ({
        ...prev,
        total: response.total,
        current: response.page,
        pageSize: response.page_size,
      }));
    } catch (error) {
      message.error('获取渠道付费数据失败');
      console.error('获取渠道付费数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params: Partial<ChannelPaymentsQueryParams> = {
      page: 1, // 搜索时重置到第一页
    };

    if (values.channel) {
      params.channel = values.channel;
    }

    if (values.timeRange && values.timeRange.length === 2) {
      params.start_time = values.timeRange[0].toISOString();
      params.end_time = values.timeRange[1].toISOString();
    }

    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData(params);
  };

  // 重置搜索
  const handleReset = () => {
    if (adminInfo?.is_super_admin) {
      // 超级管理员可以完全重置
      form.resetFields();
      setPagination(prev => ({ ...prev, current: 1 }));
      fetchData({ page: 1 });
    } else {
      // 普通管理员重置时保持默认渠道
      const defaultChannel = adminInfo?.channels?.[0];
      form.resetFields();
      if (defaultChannel) {
        form.setFieldsValue({ channel: defaultChannel });
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchData({ page: 1, channel: defaultChannel });
      } else {
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchData({ page: 1 });
      }
    }
  };

  // 表格分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    const newPagination = { ...pagination, current: page, pageSize };
    setPagination(newPagination);
    
    const values = form.getFieldsValue();
    const params: Partial<ChannelPaymentsQueryParams> = {
      page,
      page_size: pageSize,
    };

    if (values.channel) {
      params.channel = values.channel;
    }

    if (values.timeRange && values.timeRange.length === 2) {
      params.start_time = values.timeRange[0].toISOString();
      params.end_time = values.timeRange[1].toISOString();
    }

    fetchData(params);
  };

  // 初始化数据和默认渠道
  useEffect(() => {
    if (adminInfo?.is_super_admin) {
      // 超级管理员不设置默认渠道，查询所有数据
      fetchData();
    } else if (adminInfo?.channels && adminInfo.channels.length > 0) {
      // 普通管理员设置默认渠道为第一个
      const defaultChannel = adminInfo.channels[0];
      form.setFieldsValue({ channel: defaultChannel });

      // 使用默认渠道查询数据
      fetchData({ channel: defaultChannel });
    } else {
      // 如果没有渠道信息，直接查询
      fetchData();
    }
  }, [adminInfo?.channels, adminInfo?.is_super_admin]);

  return (
    <div className="p-6">
      {/* 统计卡片 */}
      <Row gutter={16} className="mb-4">
        <Col span={12}>
          <Card>
            <Statistic
              title="合计付费金额"
              value={totalAmount}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="元"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Statistic
              title="付费记录数"
              value={pagination.total}
              suffix="条"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      <Card className="mb-4">
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          className="mb-4"
        >
          <Form.Item name="channel" label="渠道号">
            <Select
              placeholder="请选择渠道号"
              style={{ width: 200 }}
              allowClear={adminInfo?.is_super_admin || false}
            >
              {adminInfo?.channels?.map((channel: string) => (
                <Option key={channel} value={channel}>
                  {channel}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="timeRange" label="时间范围">
            <RangePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              style={{ width: 300 }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
          </Form.Item>

          <Form.Item>
            <Button onClick={handleReset} icon={<ReloadOutlined />}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card title="渠道付费列表">
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default ChannelPayments;

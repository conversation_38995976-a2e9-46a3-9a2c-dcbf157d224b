import React from 'react';
import { Form, Input, Button, Checkbox, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAdminStore } from '@/store/adminStore';
import type { AdminLoginParams } from '@/api/types/auth';

interface LoginForm extends AdminLoginParams {
  remember?: boolean;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading } = useAdminStore();

  const onFinish = async (values: LoginForm) => {
    try {
      // 调用 store 的登录方法
      await login({
        username: values.username,
        password: values.password,
      });

      message.success('登录成功！');
      // 登录成功后跳转到渠道用户页面
      navigate('/backend/channel-users');
    } catch (error: any) {
      // 处理登录错误
      const errorMessage = error?.response?.data?.message || error?.message || '登录失败，请重试！';
      message.error(errorMessage);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* 左侧品牌区域 - 占2/3宽度 */}
      <div className="w-2/3 bg-[linear-gradient(154deg,#07070915_30%,hsl(var(--primary)/30%)_48%,#07070915_64%)] relative flex items-center justify-center p-12">
        {/* 模糊背景层 */}
        <div className="absolute inset-0 blur-[100px] bg-[linear-gradient(154deg,#07070915_30%,hsl(var(--primary)/30%)_48%,#07070915_64%)]"></div>

        {/* 内容层 - 不受模糊影响 */}
        <div className="relative z-10 text-center text-white">
          {/* SVG 图标区域 */}
          <div className="mb-8">
            <div className="w-48 h-48 mx-auto mb-6">
              <img
                src="/backend/login.svg"
                alt="Login Icon"
                className="w-full h-full animate-[float_5s_linear_0s_infinite] drop-shadow-lg"
              />
            </div>
          </div>

          {/* 标题和描述 */}
          <h1 className="text-4xl font-bold mb-4 drop-shadow-lg">熊猫简历管理平台</h1>
          <p className="text-xl opacity-90 mb-2 drop-shadow-md">专业简历制作，助力职场成功</p>
        </div>
      </div>

      {/* 右侧登录表单区域 - 占1/3宽度 */}
      <div className="w-1/3 bg-white flex items-center justify-center p-8">
        <div className="w-full max-w-sm">
          {/* 欢迎标题 */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">欢迎回来 👋</h2>
            <p className="text-gray-500">请输入您的账号和密码进行登录</p>
          </div>

          {/* 登录表单 */}
          <Form
            name="login"
            onFinish={onFinish}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名!' }]}
            >
              <Input
                prefix={<UserOutlined className="text-gray-400" />}
                placeholder="Super"
                className="h-12 rounded-lg"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码!' }]}
            >
              <Input.Password
                prefix={<LockOutlined className="text-gray-400" />}
                placeholder="••••••"
                className="h-12 rounded-lg"
              />
            </Form.Item>

            <div className="mb-6">
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>记住密码</Checkbox>
              </Form.Item>
            </div>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                className="w-full h-12 rounded-lg border-none text-lg font-medium"
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          {/* 版权信息 */}
          <div className="text-center mt-12">
            <p className="text-gray-400 text-xs">Copyright © {new Date().getFullYear()} Pandaresume</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;

import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { RouterProvider } from 'react-router-dom'
import { ConfigProvider, theme } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { router } from '@/router'
import '@/index.css'

// 设置 dayjs 为中文
dayjs.locale('zh-cn')

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: theme.defaultAlgorithm, // 使用默认的 light 主题
        token: {
          colorPrimary: '#824dfc', // 主色调
          colorInfo: '#824dfc', // 信息色
          colorSuccess: '#52c41a', // 成功色
          colorWarning: '#faad14', // 警告色
          colorError: '#ff4d4f', // 错误色
        },
        components: {
          Layout: {
            headerBg: '#ffffff', // Header 背景色为白色
            headerColor: '#000000', // Header 文字颜色为黑色
            siderBg: '#ffffff', // Sider 背景色为白色
          },
          Menu: {
            itemBg: '#ffffff', // 菜单项背景色
            itemColor: '#000000', // 菜单项文字颜色
            itemSelectedBg: '#f0ebff', // 选中项背景色（紫色系）
            itemSelectedColor: '#824dfc', // 选中项文字颜色
            itemHoverBg: '#f5f5f5', // 悬停背景色
            itemHoverColor: '#824dfc', // 悬停文字颜色
          },
          Button: {
            colorPrimary: '#824dfc', // 按钮主色
            colorPrimaryHover: '#9d7afd', // 按钮悬停色
            colorPrimaryActive: '#6b46c1', // 按钮激活色
          },
        },
      }}
    >
      <RouterProvider router={router} />
    </ConfigProvider>
  </StrictMode>,
)

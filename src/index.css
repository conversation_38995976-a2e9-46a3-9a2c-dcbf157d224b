@import "tailwindcss";

:root {
  /* 主题色 #824dfc */
  --primary: 258 95% 65%; /* 紫色主题色的 HSL 值 */
  --primary-rgb: 130, 77, 252; /* RGB 值 */
  --primary-hex: #824dfc; /* 十六进制值 */

  /* 主题色变体 */
  --primary-50: #f5f3ff;
  --primary-100: #ede9fe;
  --primary-200: #ddd6fe;
  --primary-300: #c4b5fd;
  --primary-400: #a78bfa;
  --primary-500: #824dfc;
  --primary-600: #7c3aed;
  --primary-700: #6d28d9;
  --primary-800: #5b21b6;
  --primary-900: #4c1d95;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 自定义紫色工具类 */
.text-primary {
  color: var(--primary-hex);
}

.bg-primary {
  background-color: var(--primary-hex);
}

.border-primary {
  border-color: var(--primary-hex);
}

.hover\:bg-primary:hover {
  background-color: var(--primary-hex);
}

.hover\:text-primary:hover {
  color: var(--primary-hex);
}

/* 测试样式 */
.test-red {
  background-color: red;
  color: white;
  padding: 20px;
}

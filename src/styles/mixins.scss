// 导入变量
@import './variables.scss';

// 按钮混合器
@mixin button-style($bg-color: $primary-color, $text-color: #fff) {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-base;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    opacity: 0.8;
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 卡片混合器
@mixin card-style($padding: $spacing-lg) {
  background: $background-color;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-base;
  padding: $padding;
  
  &:hover {
    box-shadow: 0 4px 12px -4px rgba(0, 0, 0, 0.15), 0 8px 20px 0 rgba(0, 0, 0, 0.1), 0 12px 32px 8px rgba(0, 0, 0, 0.08);
  }
}

// 响应式混合器
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}

// 文本省略混合器
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

import { get } from '../utils/request';
import type { 
  ChannelUsersListResponse,
  ChannelPaymentsListResponse,
  ChannelUsersQueryParams,
  ChannelPaymentsQueryParams
} from './types/channel';

// 渠道管理相关 API
export const channelApi = {
  // 获取渠道用户列表
  getChannelUsers: (params: ChannelUsersQueryParams): Promise<ChannelUsersListResponse> => {
    return get('/channel/users', { params });
  },

  // 获取渠道付费列表
  getChannelPayments: (params: ChannelPaymentsQueryParams): Promise<ChannelPaymentsListResponse> => {
    return get('/channel/payments', { params });
  },
};

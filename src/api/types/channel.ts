// 渠道管理相关类型定义

// 用户状态枚举
export const UserStatus = {
  NORMAL: 1,
  ABNORMAL: 2,
} as const;

export type UserStatus = typeof UserStatus[keyof typeof UserStatus];

// 用户类型枚举
export const UserType = {
  GUEST: 1,      // 游客
  NORMAL: 2,     // 普通用户
  MEMBER: 3,     // 会员
} as const;

export type UserType = typeof UserType[keyof typeof UserType];

// 渠道用户查询参数
export interface ChannelUsersQueryParams {
  page?: number;           // 页码，默认1
  page_size?: number;      // 每页条数，默认10
  channel?: string;        // 渠道号，如 "web"
  start_time?: string;     // 开始时间，如 "2024-01-01T00:00:00Z"
  end_time?: string;       // 结束时间，如 "2024-12-31T23:59:59Z"
}

// 渠道付费查询参数
export interface ChannelPaymentsQueryParams {
  page?: number;           // 页码，默认1
  page_size?: number;      // 每页条数，默认10
  channel?: string;        // 渠道号，如 "web"
  start_time?: string;     // 开始时间，如 "2024-01-01T00:00:00Z"
  end_time?: string;       // 结束时间，如 "2024-12-31T23:59:59Z"
}

// 渠道用户信息
export interface ChannelUserResponse {
  id: number;              // 用户ID
  username: string;        // 用户名
  email: string;           // 邮箱
  phone: string;           // 手机号
  channel: string;         // 渠道号
  status: UserStatus;      // 用户状态
  user_type: UserType;     // 用户类型
  created_at: string;      // 创建时间
}

// 渠道用户列表响应
export interface ChannelUsersListResponse {
  list: ChannelUserResponse[];  // 用户列表
  page: number;                 // 当前页码
  page_size: number;            // 每页条数
  total: number;                // 总记录数
}

// 支付方式枚举
export const PaymentMethod = {
  WECHAT: 1,    // 微信支付
  ALIPAY: 2,    // 支付宝
} as const;

export type PaymentMethod = typeof PaymentMethod[keyof typeof PaymentMethod];

// 支付状态枚举
export const PaymentStatus = {
  PENDING: 1,        // 待支付
  PROCESSING: 2,     // 支付处理中
  SUCCESS: 3,        // 支付成功
  FAILED: 4,         // 支付失败
  TIMEOUT: 5,        // 支付超时
} as const;

export type PaymentStatus = typeof PaymentStatus[keyof typeof PaymentStatus];

// 渠道付费信息
export interface ChannelPaymentResponse {
  id: number;                    // 付费记录ID
  user_id: number;               // 用户ID
  username: string;              // 用户名
  channel: string;               // 渠道号
  order_no: string;              // 订单号
  amount: number;                // 付费金额
  payment_method: PaymentMethod; // 支付方式
  payment_status: PaymentStatus; // 支付状态
  order_created_at: string;      // 订单创建时间
  user_created_at: string;       // 用户创建时间
}

// 渠道付费列表响应
export interface ChannelPaymentsListResponse {
  list: ChannelPaymentResponse[];  // 付费列表
  page: number;                    // 当前页码
  page_size: number;               // 每页条数
  total: number;                   // 总记录数
  total_amount: number;            // 合计付费金额
}

// 管理员相关类型定义

// 基础响应类型
export interface BaseResponse {
  code: number;
  message: string;
}

// 管理员信息
export interface AdminInfo {
  id: number;
  username: string;
  is_super_admin: boolean;
  channels: string[];
  created_at: string;
}

// 管理员信息响应
export interface AdminInfoResponse extends AdminInfo {
  // 继承 AdminInfo 的所有属性
}

// 管理员修改密码请求
export interface AdminChangePasswordRequest {
  old_password: string;  // 旧密码
  new_password: string;  // 新密码
}

// 管理员修改密码响应
export interface AdminChangePasswordResponse {
  // 修改成功后返回空对象或成功信息
}

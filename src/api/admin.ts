import { get, post } from '../utils/request';
import type {
  AdminInfoResponse,
  AdminChangePasswordRequest,
  AdminChangePasswordResponse
} from './types/admin';

// 管理员管理相关 API
export const adminApi = {
  // 获取当前管理员信息
  getAdminInfo: (): Promise<AdminInfoResponse> => {
    return get('/admin/info');
  },

  // 退出登录
  logout: (): Promise<void> => {
    return post('/admin/logout');
  },

  // 修改管理员密码
  changePassword: (data: AdminChangePasswordRequest): Promise<AdminChangePasswordResponse> => {
    return post('/admin/change-password', data);
  },
};

import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAdminStore } from '@/store/adminStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, token, isLoading, getAdminInfo } = useAdminStore();

  useEffect(() => {
    // 检查 localStorage 中的 token（处理页面刷新的情况）
    const storedToken = localStorage.getItem('admin_token');

    // 如果有 token 但没有认证状态，尝试获取用户信息
    if ((token || storedToken) && !isAuthenticated && !isLoading) {
      console.log('ProtectedRoute: 尝试获取用户信息...');
      getAdminInfo().catch((error) => {
        console.error('ProtectedRoute: 获取用户信息失败', error);
        // 获取用户信息失败，清除认证状态
      });
    }
  }, [token, isAuthenticated, isLoading, getAdminInfo]);

  // 正在加载中
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  // 检查认证状态
  const storedToken = localStorage.getItem('admin_token');

  // 未认证，重定向到登录页
  if (!isAuthenticated || (!token && !storedToken)) {
    console.log('ProtectedRoute: 未认证，重定向到登录页');
    return <Navigate to="/backend/login" state={{ from: location }} replace />;
  }

  // 已认证，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;

import React from 'react';
import { Modal, Form, Input, message } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { adminApi } from '@/api/admin';
import type { AdminChangePasswordRequest } from '@/api/types/admin';

interface ChangePasswordModalProps {
  open: boolean;
  onCancel: () => void;
}

interface FormValues {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({
  open,
  onCancel,
}) => {
  const [form] = Form.useForm<FormValues>();
  const [loading, setLoading] = React.useState(false);

  // 处理表单提交
  const handleSubmit = async (values: FormValues) => {
    try {
      setLoading(true);
      
      const requestData: AdminChangePasswordRequest = {
        old_password: values.oldPassword,
        new_password: values.newPassword,
      };

      await adminApi.changePassword(requestData);
      
      message.success('密码修改成功！');
      form.resetFields();
      onCancel();
    } catch (error: any) {
      console.error('修改密码失败:', error);
      
      // 根据错误类型显示不同的错误信息
      if (error?.code === 401) {
        message.error('旧密码错误，请重新输入');
      } else if (error?.message) {
        message.error(error.message);
      } else {
        message.error('修改密码失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="修改密码"
      open={open}
      onCancel={handleCancel}
      onOk={() => form.submit()}
      confirmLoading={loading}
      okText="确认修改"
      cancelText="取消"
      width={400}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
        className="mt-4"
      >
        <Form.Item
          name="oldPassword"
          label="当前密码"
          rules={[
            { required: true, message: '请输入当前密码' },
            { min: 6, message: '密码长度至少6位' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className="text-gray-400" />}
            placeholder="请输入当前密码"
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="newPassword"
          label="新密码"
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 6, message: '密码长度至少6位' },
            { max: 20, message: '密码长度不能超过20位' },
            {
              pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
              message: '密码必须包含字母和数字',
            },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className="text-gray-400" />}
            placeholder="请输入新密码"
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="确认新密码"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: '请确认新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className="text-gray-400" />}
            placeholder="请再次输入新密码"
            size="large"
          />
        </Form.Item>
      </Form>

      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <div className="text-sm text-blue-600">
          <div className="font-medium mb-1">密码要求：</div>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>长度6-20位</li>
            <li>必须包含字母和数字</li>
            <li>可包含特殊字符 @$!%*?&</li>
          </ul>
        </div>
      </div>
    </Modal>
  );
};

export default ChangePasswordModal;

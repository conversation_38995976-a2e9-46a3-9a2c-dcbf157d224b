import React from 'react';
import { <PERSON><PERSON>, Breadcrumb } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ReloadOutlined,
  AppstoreOutlined,
  DashboardOutlined,
  UserOutlined,
  CreditCardOutlined,
} from '@ant-design/icons';
import { useLocation } from 'react-router-dom';

interface BreadcrumbHeaderProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
  onRefresh?: () => void;
}

const BreadcrumbHeader: React.FC<BreadcrumbHeaderProps> = ({
  collapsed,
  onToggleCollapse,
  onRefresh,
}) => {
  const location = useLocation();

  // 根据路由生成面包屑数据
  const getBreadcrumbItems = () => {
    const pathname = location.pathname;

    // 根据当前路径生成对应的面包屑
    if (pathname.includes('/channel-users')) {
      return [
        {
          title: (
            <span className="flex items-center">
              <DashboardOutlined className="mr-1" />
              渠道管理
            </span>
          ),
        },
        {
          title: (
            <span className="flex items-center">
              <UserOutlined className="mr-1" />
              渠道用户
            </span>
          ),
        },
      ];
    }

    if (pathname.includes('/channel-payment')) {
      return [
        {
          title: (
            <span className="flex items-center">
              <DashboardOutlined className="mr-1" />
              渠道管理
            </span>
          ),
        },
        {
          title: (
            <span className="flex items-center">
              <CreditCardOutlined className="mr-1" />
              渠道付费
            </span>
          ),
        },
      ];
    }

    // 默认概览页面（如果有其他页面的话）
    return [
      {
        title: (
          <span className="flex items-center">
            <AppstoreOutlined className="mr-1" />
            概览
          </span>
        ),
      },
    ];
  };

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    } else {
      // 默认刷新页面
      window.location.reload();
    }
  };

  return (
    <div className="flex items-center space-x-1">
      {/* 菜单折叠按钮 */}
      <Button
        type="text"
        icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        onClick={onToggleCollapse}
        className="text-gray-600 hover:text-primary hover:bg-gray-50"
        size="middle"
      />

      {/* 刷新按钮 */}
      <Button
        type="text"
        icon={<ReloadOutlined />}
        onClick={handleRefresh}
        className="text-gray-600 hover:text-primary hover:bg-gray-50"
        size="middle"
      />

      {/* 面包屑导航 */}
      <Breadcrumb
        items={getBreadcrumbItems()}
        className="text-gray-600 ml-1"
        separator=">"
      />
    </div>
  );
};

export default BreadcrumbHeader;

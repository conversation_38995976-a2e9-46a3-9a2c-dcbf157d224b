{"name": "resume-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@tanstack/react-query": "^5.84.1", "antd": "^5.26.7", "axios": "^1.11.0", "dayjs": "^1.11.13", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/vite": "^4.1.11", "@types/node": "^24.1.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "sass": "^1.89.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}
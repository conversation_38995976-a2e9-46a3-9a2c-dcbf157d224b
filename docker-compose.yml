version: '3.8'

services:
  resume-admin:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: resume-admin
    restart: unless-stopped
    ports:
      - "3000:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      # 可选：挂载日志目录到宿主机
      - ./logs:/var/log/nginx
    networks:
      - resume-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  resume-network:
    driver: bridge

# 可选：如果需要持久化日志
volumes:
  nginx-logs:
    driver: local
